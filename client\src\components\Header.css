.header {
  background-color: #00796b;
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header .logo {
  color: white;
  text-decoration: none;
  font-size: 1.8em;
  font-weight: bold;
}

.header .nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.header .nav ul li {
  margin-left: 25px;
}

.header .nav ul li a {
  color: white;
  text-decoration: none;
  font-size: 1.1em;
  transition: color 0.3s ease;
}

.header .nav ul li a:hover {
  color: #e0f7fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    text-align: center;
  }

  .header .nav ul {
    margin-top: 15px;
    flex-direction: column;
    align-items: center;
  }

  .header .nav ul li {
    margin: 10px 0;
  }
}