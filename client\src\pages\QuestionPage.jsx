import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import './QuestionPage.css';

function QuestionPage() {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { subject } = useParams();

  useEffect(() => {
    const fetchQuestions = async () => {
      try {
        const res = await axios.get(`/api/questions/previous?subject=${subject}`);
        setQuestions(res.data);
        setLoading(false);
      } catch (err) {
        setError(err.response && err.response.data.message
          ? err.response.data.message
          : err.message);
        setLoading(false);
      }
    };

    fetchQuestions();
  }, [subject]);

  if (loading) {
    return <div className="question-container">Loading...</div>;
  }

  if (error) {
    return <div className="question-container">Error: {error}</div>;
  }

  return (
    <div className="question-container">
      <h2>Previous Year Questions - {subject.charAt(0).toUpperCase() + subject.slice(1)}</h2>
      <div className="question-list">
        {questions.length > 0 ? (
          questions.map((question) => (
            <div className="question-item" key={question._id}>
              <div>
                <h3>{question.title}</h3>
                <p>Standard: {question.standard}</p>
              </div>
              {question.pdfUrl && (
                <a href={question.pdfUrl} target="_blank" rel="noopener noreferrer" className="btn small">View PDF</a>
              )}
            </div>
          ))
        ) : (
          <p>No previous year questions available for this subject.</p>
        )}
      </div>
    </div>
  );
}

export default QuestionPage;