.payment-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 20px;
}

.payment-card {
  background-color: #ffffff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 900px;
  text-align: center;
}

.payment-card h2 {
  font-size: 2.5em;
  color: #00796b;
  margin-bottom: 40px;
}

.pricing-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.plan-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 30px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.plan-item:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.plan-item.featured {
  border-color: #00796b;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
}

.plan-item h3 {
  font-size: 1.8em;
  color: #00796b;
  margin-bottom: 15px;
}

.plan-item .price {
  font-size: 2.2em;
  font-weight: bold;
  color: #004d40;
  margin-bottom: 20px;
}

.plan-item ul {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
  text-align: left;
  flex-grow: 1;
}

.plan-item ul li {
  margin-bottom: 10px;
  color: #555;
  font-size: 1.1em;
}

.plan-item .btn.primary {
  background-color: #00796b;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1.1em;
  width: 100%;
  transition: background-color 0.3s ease;
}

.plan-item .btn.primary:hover {
  background-color: #004d40;
}

.payment-note {
  font-size: 1em;
  color: #777;
  margin-top: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .payment-card {
    padding: 20px;
  }

  .payment-card h2 {
    font-size: 2em;
  }

  .pricing-plans {
    grid-template-columns: 1fr;
  }

  .plan-item {
    padding: 20px;
  }

  .plan-item h3 {
    font-size: 1.5em;
  }

  .plan-item .price {
    font-size: 1.8em;
  }
}