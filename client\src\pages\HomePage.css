.homepage {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

.hero-section {
  background-color: #e0f7fa;
  padding: 60px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
  text-align: center;
}

.hero-content {
  max-width: 600px;
}

.hero-content h1 {
  font-size: 2.8em;
  color: #00796b;
  margin-bottom: 15px;
}

.hero-content p {
  font-size: 1.2em;
  margin-bottom: 30px;
}

.cta-buttons .btn {
  background-color: #00796b;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  margin: 0 10px;
  transition: background-color 0.3s ease;
}

.cta-buttons .btn:hover {
  background-color: #004d40;
}

.cta-buttons .btn.secondary {
  background-color: #00796b;
  border: 2px solid #00796b;
  color: white;
}

.cta-buttons .btn.secondary:hover {
  background-color: #004d40;
  border-color: #004d40;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.features-section, .testimonials-section, .featured-content-section {
  padding: 60px 20px;
  text-align: center;
}

.features-section h2, .testimonials-section h2, .featured-content-section h2 {
  font-size: 2.5em;
  color: #00796b;
  margin-bottom: 40px;
}

.feature-cards, .content-previews {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
}

.card, .preview-card, .testimonial-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 30px;
  max-width: 300px;
  text-align: left;
}

.card h3, .preview-card h3 {
  color: #00796b;
  margin-bottom: 10px;
}

.testimonial-card p {
  font-style: italic;
  margin-bottom: 10px;
}

.testimonial-card h4 {
  color: #00796b;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    flex-direction: column;
  }

  .hero-content h1 {
    font-size: 2em;
  }

  .hero-content p {
    font-size: 1em;
  }

  .cta-buttons .btn {
    padding: 10px 20px;
    margin: 10px 0;
  }

  .features-section h2, .testimonials-section h2, .featured-content-section h2 {
    font-size: 2em;
  }

  .card, .preview-card, .testimonial-card {
    max-width: 90%;
  }
}