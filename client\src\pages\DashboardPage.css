.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f0f2f5;
}

.sidebar {
  width: 250px;
  background-color: #00796b;
  color: white;
  padding: 20px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar h2 {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.sidebar ul {
  list-style: none;
  padding: 0;
}

.sidebar ul li {
  margin-bottom: 15px;
}

.sidebar ul li a {
  color: white;
  text-decoration: none;
  font-size: 1.1em;
  display: block;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.sidebar ul li a:hover {
  background-color: #004d40;
}

.main-content {
  flex-grow: 1;
  padding: 20px;
}

.dashboard-section {
  background-color: #ffffff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.dashboard-section h2 {
  color: #00796b;
  margin-bottom: 20px;
  font-size: 1.8em;
  border-bottom: 2px solid #e0f7fa;
  padding-bottom: 10px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.video-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  text-align: center;
  padding-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.video-card h3 {
  font-size: 1.2em;
  margin: 15px 0 5px;
  color: #333;
}

.video-card p {
  color: #555;
  font-size: 0.9em;
  margin-bottom: 10px;
}

.video-thumbnail img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.subject-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.subject-link {
  background-color: #e0f7fa;
  color: #00796b;
  padding: 10px 20px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s, color 0.3s;
}

.subject-link:hover {
  background-color: #00796b;
  color: white;
}

.question-list .question-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.question-list .question-item:last-child {
  border-bottom: none;
}

.question-item h3 {
  font-size: 1.1em;
  color: #333;
}

.question-item p {
  color: #666;
  font-size: 0.9em;
}

.btn.small {
  background-color: #00796b;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.3s ease;
}

.btn.small:hover {
  background-color: #004d40;
}

.calendar-placeholder {
  text-align: center;
  padding: 50px;
  color: #777;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    padding: 15px;
  }

  .sidebar ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .sidebar ul li {
    margin: 5px 10px;
  }

  .main-content {
    padding: 15px;
  }

  .video-grid {
    grid-template-columns: 1fr;
  }
}