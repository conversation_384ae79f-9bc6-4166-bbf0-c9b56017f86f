import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import './DashboardPage.css';

function DashboardPage() {
  const [videos, setVideos] = useState([]);
  const [previousQuestions, setPreviousQuestions] = useState([]);
  const [modelQuestions, setModelQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchContent = async () => {
      try {
        // For demonstration, bypassing login check. In a real app, authentication is crucial.
        // const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        // if (!userInfo || !userInfo.token) {
        //   navigate('/login');
        //   return;
        // }

        // const config = {
        //   headers: {
        //     Authorization: `Bearer ${userInfo.token}`,
        //   },
        // };

        // Check trial status and payment status (if applicable, without userInfo)
        // This part might need adjustment if it relies heavily on userInfo
        // if (!userInfo.isPaid && (!userInfo.isTrialActive || new Date(userInfo.trialEnds) < new Date())) {
        //   navigate('/payment');
        //   return;
        // }

        const videoRes = await axios.get('/api/videos'); 
        setVideos(videoRes.data);

        const prevQuesRes = await axios.get('/api/questions/previous'); 
        setPreviousQuestions(prevQuesRes.data);

        const modelQuesRes = await axios.get('/api/questions/model'); 
        setModelQuestions(modelQuesRes.data);

        setLoading(false);
      } catch (err) {
        setError(err.response && err.response.data.message
          ? err.response.data.message
          : err.message);
        setLoading(false);
        // If access denied, redirect to payment (if applicable)
        // if (err.response && err.response.status === 403) {
        //   navigate('/payment');
        // }
      }
    };

    fetchContent();
  }, []); // Removed navigate from dependency array as it's not used for redirection here

  if (loading) {
    return <div className="dashboard-container">Loading...</div>;
  }

  if (error) {
    return <div className="dashboard-container">Error: {error}</div>;
  }

  return (
    <div className="dashboard-container">
      <aside className="sidebar">
        <h2>Dashboard</h2>
        <nav>
          <ul>
            <li><a href="#videos">Video Tutorials</a></li>
            <li><a href="#previous-questions">Previous Year Questions</a></li>
            <li><a href="#model-questions">Model Questions</a></li>
            <li><a href="#live-sessions">Live Sessions</a></li>
          </ul>
        </nav>
      </aside>
      <main className="main-content">
        <section id="videos" className="dashboard-section">
          <h2>Video Tutorials</h2>
          <div className="video-grid">
            {videos.length > 0 ? (
              videos.map((video) => (
                <div className="video-card" key={video._id}>
                  <h3>{video.title}</h3>
                  <p>Subject: {video.subject}</p>
                  <div className="video-thumbnail">
                    {/* In a real app, embed video player or link to video page */}
                    <img src={`https://img.youtube.com/vi/${video.url.split('v=')[1]}/0.jpg`} alt={video.title} />
                  </div>
                  <a href={video.url} target="_blank" rel="noopener noreferrer" className="btn small">Watch Video</a>
                </div>
              ))
            ) : (
              <p>No videos available.</p>
            )}
          </div>
        </section>

        <section id="previous-questions" className="dashboard-section">
          <h2>Previous Year Questions</h2>
          {/* Subject-wise navigation */}
          <div className="subject-nav">
            <a href="/questions/physics" className="subject-link">Physics</a>
            <a href="/questions/chemistry" className="subject-link">Chemistry</a>
            <a href="/questions/mathematics" className="subject-link">Mathematics</a>
            <a href="/questions/biology" className="subject-link">Biology</a>
            <a href="/questions/computer-science" className="subject-link">Computer Science</a>
            <a href="/questions/english" className="subject-link">English</a>
            {/* Add more subjects as needed */}
          </div>
          <div className="question-list">
            {previousQuestions.length > 0 ? (
              previousQuestions.map((question) => (
                <div className="question-item" key={question._id}>
                  <div>
                    <h3>{question.title}</h3>
                    <p>Subject: {question.subject} | Standard: {question.standard}</p>
                  </div>
                  {question.pdfUrl && (
                    <a href={question.pdfUrl} target="_blank" rel="noopener noreferrer" className="btn small">View PDF</a>
                  )}
                </div>
              ))
            ) : (
              <p>No previous year questions available.</p>
            )}
          </div>
        </section>

        <section id="model-questions" className="dashboard-section">
          <h2>Model Questions</h2>
          <div className="question-list">
            {modelQuestions.length > 0 ? (
              modelQuestions.map((question) => (
                <div className="question-item" key={question._id}>
                  <div>
                    <h3>{question.title}</h3>
                    <p>Subject: {question.subject} | Standard: {question.standard}</p>
                  </div>
                  {question.pdfUrl && (
                    <a href={question.pdfUrl} target="_blank" rel="noopener noreferrer" className="btn small">View PDF</a>
                  )}
                </div>
              ))
            ) : (
              <p>No model questions available.</p>
            )}
          </div>
        </section>

        <section id="live-sessions" className="dashboard-section">
          <h2>Live Session Calendar</h2>
          <div className="calendar-placeholder">
            <p>Upcoming live sessions will be displayed here.</p>
          </div>
        </section>
      </main>
    </div>
  );
}

export default DashboardPage;